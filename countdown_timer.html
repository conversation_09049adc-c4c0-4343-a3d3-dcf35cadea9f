<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Course Countdown Timer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 2rem;
            text-align: center;
        }

        .settings-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: #2ecc71;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s, background 0.2s;
            font-size: 1.2rem;
        }

        .settings-toggle:hover {
            transform: translateY(-2px);
            background: #27ae60;
        }

        .header {
            margin-bottom: 2rem;
            width: 100%;
            max-width: 1200px;
            transition: opacity 0.3s ease;
            display: flex;
            flex-direction: column;
        }

        textarea {
            resize: vertical;
            min-height: 80px;
            padding: 1.2rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 0.8rem;
            width: 100%;
            font-size: 1.2rem;
        }

        input {
            padding: 1.2rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 0.8rem;
            width: 100%;
            font-size: 1.2rem;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 2rem;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 0 auto;
            width: 95%;
            max-width: 1200px;
        }

        .current-time-box {
            background: rgba(255, 255, 255, 0.95);
            padding: 3rem;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 2rem auto;
            width: 95%;
            max-width: 1200px;
        }

        .course-title {
            font-weight: 600;
            margin-bottom: 2rem;
            color: #2c3e50;
            line-height: 1.3;
            white-space: pre-wrap;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            width: 100%;
            overflow: hidden;
            font-size: 64px;
        }

        .current-time-title {
            font-size: 3rem;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 2rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .title-line {
            white-space: nowrap;
            display: block;
        }

        #timer {
            font-size: 6rem;
            font-weight: 300;
            /* margin: 3rem 0; */
            color: #2c3e50;
            font-family: 'Courier New', Courier, monospace;
        }

        .current-time {
            font-size: 6rem;
            font-weight: 300;
            color: #3498db;
            font-family: 'Courier New', Courier, monospace;
        }

        .overdue-message {
            color: #e74c3c;
            font-size: 3rem;
            font-weight: bold;
            /* margin-bottom: 2rem; */
            display: none;
        }

        .button-group {
            display: flex;
            gap: 1.5rem;
            justify-content: center;
            margin-top: 2rem;
        }

        button {
            border: none;
            padding: 1.5rem 3rem;
            border-radius: 12px;
            font-size: 1.3rem;
            cursor: pointer;
            transition: transform 0.2s, background 0.2s;
        }

        #startButton {
            background: #3498db;
            color: white;
        }

        #pauseButton {
            background: #f1c40f;
            color: black;
        }

        #resetButton {
            background: #e74c3c;
            color: white;
        }

        button:hover {
            transform: translateY(-2px);
        }

        .overdue {
            color: #e74c3c !important;
            font-family: 'Courier New', Courier, monospace; 
        }

        @media (max-width: 480px) {
            .container, .current-time-box {
                width: 98%;
                padding: 2rem;
            }
            #timer, .current-time {
                font-size: 4rem;
            }
            .current-time-title {
                font-size: 2.5rem;
            }
            .button-group {
                flex-direction: column;
            }
            button {
                width: 100%;
                padding: 1.2rem 2rem;
                font-size: 1.1rem;
            }
            .overdue-message {
                font-size: 2.5rem;
            }
            .course-title {
                font-size: 3.5rem;
                margin-bottom: 2rem;
            }
        }
    </style>
</head>
<body>
    <button id="settingsToggle" class="settings-toggle">Hide Settings</button>
    
    <div class="header">
        <textarea id="courseTitle" placeholder="Course Title (press Enter for new lines)"></textarea>
        <input type="number" id="courseDuration" placeholder="Duration (minutes)">
        <div class="button-group">
            <button id="startButton">Start</button>
            <button id="pauseButton">Pause</button>
            <button id="resetButton">Reset</button>
        </div>
    </div>

    <div class="container">
        <h1 class="course-title" id="displayTitle">Course Countdown Timer</h1>
        <div id="overdueMessage" class="overdue-message">OVERDUE</div>
        <div id="timer">00:00:00</div>
    </div>

    <div class="current-time-box">
        <div class="current-time-title">TIME NOW</div>
        <div id="currentTime" class="current-time">00:00</div>
    </div>

    <script>
        let endTime;
        let timerInterval;
        let isPaused = false;
        let remainingTime = 0;

        const startButton = document.getElementById('startButton');
        const pauseButton = document.getElementById('pauseButton');
        const resetButton = document.getElementById('resetButton');
        const settingsToggle = document.getElementById('settingsToggle');
        const header = document.querySelector('.header');

        settingsToggle.addEventListener('click', () => {
            header.style.display = header.style.display === 'none' ? 'flex' : 'none';
            settingsToggle.textContent = header.style.display === 'none' ? 'Show Settings' : 'Hide Settings';
        });

        function updateCurrentTime() {
            const now = new Date();
            document.getElementById('currentTime').textContent = 
                `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
        }
        setInterval(updateCurrentTime, 1000);
        updateCurrentTime();

        function calculateMaxLineWidth(lines, fontSize) {
            const measurer = document.createElement('div');
            measurer.style.position = 'absolute';
            measurer.style.visibility = 'hidden';
            measurer.style.whiteSpace = 'nowrap';
            measurer.style.font = `${fontSize}px 'Segoe UI'`;
            document.body.appendChild(measurer);

            let maxWidth = 0;
            lines.forEach(line => {
                measurer.textContent = line;
                maxWidth = Math.max(maxWidth, measurer.offsetWidth);
            });
            document.body.removeChild(measurer);
            return maxWidth;
        }

        function autoSizeText() {
            const titleElement = document.getElementById('displayTitle');
            const container = document.querySelector('.container');
            const lines = titleElement.textContent.split('\n');
            const containerWidth = container.offsetWidth - 40;
            
            let low = 20;
            let high = 56;
            let bestSize = high;

            for (let i = 0; i < 10; i++) {
                const mid = Math.floor((low + high) / 2);
                const currentWidth = calculateMaxLineWidth(lines, mid);
                currentWidth <= containerWidth ? (bestSize = mid, low = mid + 1) : (high = mid - 1);
            }

            titleElement.style.fontSize = `${bestSize}px`;
        }

        function startTimer() {
            if (isPaused) {
                endTime = Date.now() + remainingTime;
                isPaused = false;
            } else {
                const title = document.getElementById('courseTitle').value || 'Course Countdown Timer';
                const duration = parseInt(document.getElementById('courseDuration').value) || 0;
                
                if (duration <= 0) return alert('Please enter valid duration');
                
                const titleElement = document.getElementById('displayTitle');
                titleElement.innerHTML = title.split('\n').map(line => 
                    `<div class="title-line">${line}</div>`
                ).join('');
                
                autoSizeText();
                remainingTime = duration * 60 * 1000;
                endTime = Date.now() + remainingTime;
            }
            
            clearInterval(timerInterval);
            timerInterval = setInterval(updateTimer, 1000);
            updateTimer();
        }

        function togglePause() {
            if (!isPaused && endTime) {
                remainingTime = endTime - Date.now();
                clearInterval(timerInterval);
                isPaused = true;
                pauseButton.textContent = 'Resume';
            } else {
                isPaused = false;
                pauseButton.textContent = 'Pause';
                startTimer();
            }
        }

        function resetTimer() {
            clearInterval(timerInterval);
            isPaused = false;
            remainingTime = 0;
            endTime = null;
            document.getElementById('timer').textContent = '00:00:00';
            document.getElementById('overdueMessage').style.display = 'none';
            document.getElementById('displayTitle').style.fontSize = '56px';
            document.getElementById('displayTitle').textContent = 'Course Countdown Timer';
            pauseButton.textContent = 'Pause';
            document.getElementById('courseTitle').value = '';
            document.getElementById('courseDuration').value = '';
            header.style.display = 'flex';
            settingsToggle.textContent = 'Hide Settings';
        }

        function updateTimer() {
            const now = Date.now();
            const remaining = endTime - now;
            const overdueMessage = document.getElementById('overdueMessage');

            if (remaining < 0) {
                const overdue = Math.abs(remaining);
                document.getElementById('timer').innerHTML = 
                    `<span class="overdue">+${Math.floor(overdue/(36e5)).toString().padStart(2,'0')}:` +
                    `${Math.floor((overdue%36e5)/6e4).toString().padStart(2,'0')}:` +
                    `${Math.floor((overdue%6e4)/1e3).toString().padStart(2,'0')}</span>`;
                overdueMessage.style.display = 'block';
                return;
            }

            overdueMessage.style.display = 'none';
            document.getElementById('timer').innerHTML = 
                `${Math.floor(remaining/36e5).toString().padStart(2,'0')}:` +
                `${Math.floor((remaining%36e5)/6e4).toString().padStart(2,'0')}:` +
                `${Math.floor((remaining%6e4)/1e3).toString().padStart(2,'0')}`;
        }

        startButton.addEventListener('click', startTimer);
        pauseButton.addEventListener('click', togglePause);
        resetButton.addEventListener('click', resetTimer);
        window.addEventListener('resize', () => {
            if (document.getElementById('displayTitle').textContent !== 'Course Countdown Timer') {
                autoSizeText();
            }
        });
    </script>
</body>
</html>