<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Course Countdown Timer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 2rem;
            text-align: center;
            transition: margin-left 0.3s ease;
        }

        .main-content {
            width: 100%;
            transition: margin-left 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .main-content.shifted {
            margin-left: 320px;
        }

        .panel-toggle {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: #3498db;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s, background 0.2s;
            font-size: 1.2rem;
        }

        .panel-toggle:hover {
            transform: translateY(-2px);
            background: #2980b9;
        }

        .settings-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: #2ecc71;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s, background 0.2s;
            font-size: 1.2rem;
        }

        .settings-toggle:hover {
            transform: translateY(-2px);
            background: #27ae60;
        }

        .side-panel {
            position: fixed;
            top: 0;
            left: -320px;
            width: 320px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            transition: left 0.3s ease;
            z-index: 999;
            overflow-y: auto;
            padding: 80px 20px 20px 20px;
        }

        .side-panel.open {
            left: 0;
        }

        .timer-list {
            margin-bottom: 2rem;
        }

        .timer-item {
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: background 0.2s, transform 0.2s;
        }

        .timer-item:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
        }

        .timer-item.active {
            background: #3498db;
            color: white;
            border-color: #2980b9;
        }

        .timer-item h4 {
            margin: 0 0 0.5rem 0;
            font-size: 1.1rem;
        }

        .timer-item p {
            margin: 0.25rem 0;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .timer-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .timer-actions button {
            padding: 0.3rem 0.8rem;
            font-size: 0.8rem;
            border-radius: 4px;
        }

        .start-btn {
            background: #3498db;
            color: white;
        }

        .edit-btn {
            background: #f39c12;
            color: white;
        }

        .delete-btn {
            background: #e74c3c;
            color: white;
        }

        .add-timer-btn {
            width: 100%;
            background: #2ecc71;
            color: white;
            padding: 1rem;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            margin-bottom: 1rem;
        }

        .add-timer-btn:hover {
            background: #27ae60;
        }

        .timer-form {
            background: rgba(255, 255, 255, 0.9);
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: none;
        }

        .timer-form.show {
            display: block;
        }

        .timer-form input, .timer-form textarea {
            width: 100%;
            margin: 0.5rem 0;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }

        .timer-form textarea {
            min-height: 60px;
            resize: vertical;
        }

        .timer-edit-form {
            background: rgba(240, 240, 240, 0.9);
            padding: 1rem;
            margin-top: 1rem;
            border-radius: 8px;
            border: 2px solid #3498db;
        }

        .timer-edit-form input {
            width: 100%;
            margin: 0.3rem 0;
            padding: 0.6rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .timer-edit-form label {
            display: block;
            font-size: 0.8rem;
            font-weight: bold;
            color: #2c3e50;
            margin: 0.5rem 0 0.2rem 0;
        }

        .import-export-section {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .import-btn, .export-btn {
            flex: 1;
            background: #9b59b6;
            color: white;
            padding: 0.8rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
        }

        .import-btn:hover, .export-btn:hover {
            background: #8e44ad;
        }

        #csvFileInput {
            display: none;
        }

        .form-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .save-btn, .cancel-btn {
            padding: 0.6rem 1.2rem;
            font-size: 0.9rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            flex: 1;
        }

        .save-btn {
            background: #2ecc71;
            color: white;
        }

        .cancel-btn {
            background: #95a5a6;
            color: white;
        }

        .header {
            margin-bottom: 2rem;
            width: 100%;
            max-width: 1200px;
            transition: opacity 0.3s ease;
            display: flex;
            flex-direction: column;
        }

        textarea {
            resize: vertical;
            min-height: 80px;
            padding: 1.2rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 0.8rem;
            width: 100%;
            font-size: 1.2rem;
        }

        input {
            padding: 1.2rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 0.8rem;
            width: 100%;
            font-size: 1.2rem;
        }

        input[type="checkbox"] {
            width: 20px;
            height: 20px;
            padding: 0;
            margin: 0;
            cursor: pointer;
        }

        label {
            cursor: pointer;
            user-select: none;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 2rem;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 0 auto;
            width: 95%;
            max-width: 1200px;
        }

        .current-time-box {
            background: rgba(255, 255, 255, 0.95);
            padding: 3rem;
            border-radius: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 2rem auto;
            width: 95%;
            max-width: 1200px;
        }

        .course-title {
            font-weight: 600;
            margin-bottom: 2rem;
            color: #2c3e50;
            line-height: 1.3;
            white-space: pre-wrap;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            width: 100%;
            overflow: hidden;
            font-size: 64px;
        }

        .current-time-title {
            font-size: 3rem;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 2rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .title-line {
            white-space: nowrap;
            display: block;
        }

        #timer {
            font-size: 6rem;
            font-weight: 300;
            /* margin: 3rem 0; */
            color: #2c3e50;
            font-family: 'Courier New', Courier, monospace;
        }

        .current-time {
            font-size: 6rem;
            font-weight: 300;
            color: #3498db;
            font-family: 'Courier New', Courier, monospace;
        }

        .overdue-message {
            color: #e74c3c;
            font-size: 3rem;
            font-weight: bold;
            /* margin-bottom: 2rem; */
            display: none;
        }

        .button-group {
            display: flex;
            gap: 1.5rem;
            justify-content: center;
            margin-top: 2rem;
        }

        button {
            border: none;
            padding: 1.5rem 3rem;
            border-radius: 12px;
            font-size: 1.3rem;
            cursor: pointer;
            transition: transform 0.2s, background 0.2s;
        }

        #startButton {
            background: #3498db;
            color: white;
        }

        #pauseButton {
            background: #f1c40f;
            color: black;
        }

        #resetButton {
            background: #e74c3c;
            color: white;
        }

        button:hover {
            transform: translateY(-2px);
        }

        .overdue {
            color: #e74c3c !important;
            font-family: 'Courier New', Courier, monospace; 
        }

        @media (max-width: 768px) {
            .side-panel {
                width: 280px;
                left: -280px;
            }

            .main-content.shifted {
                margin-left: 280px;
            }
        }

        @media (max-width: 480px) {
            .container, .current-time-box {
                width: 98%;
                padding: 2rem;
            }
            #timer, .current-time {
                font-size: 4rem;
            }
            .current-time-title {
                font-size: 2.5rem;
            }
            .button-group {
                flex-direction: column;
            }
            button {
                width: 100%;
                padding: 1.2rem 2rem;
                font-size: 1.1rem;
            }
            .overdue-message {
                font-size: 2.5rem;
            }
            .course-title {
                font-size: 3.5rem;
                margin-bottom: 2rem;
            }

            .side-panel {
                width: 100%;
                left: -100%;
            }

            .main-content.shifted {
                margin-left: 0;
            }

            .panel-toggle, .settings-toggle {
                padding: 0.8rem 1.5rem;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <button id="panelToggle" class="panel-toggle">Timers</button>
    <button id="settingsToggle" class="settings-toggle">Hide Settings</button>

    <div id="sidePanel" class="side-panel">
        <div class="import-export-section">
            <button id="importBtn" class="import-btn">Import CSV</button>
            <button id="exportBtn" class="export-btn">Export CSV</button>
            <input type="file" id="csvFileInput" accept=".csv" />
        </div>

        <button id="addTimerBtn" class="add-timer-btn">+ Add New Timer</button>

        <div id="timerForm" class="timer-form">
            <input type="text" id="formTitle" placeholder="Timer Title">
            <input type="number" id="formDuration" placeholder="Duration (minutes)">
            <input type="time" id="formStartTime" placeholder="Start Time">
            <input type="time" id="formEndTime" placeholder="End Time">
            <div style="display: flex; align-items: center; gap: 0.5rem; margin: 0.5rem 0;">
                <input type="checkbox" id="formAutoStart" style="width: auto; margin: 0;">
                <label for="formAutoStart">Auto Start</label>
            </div>
            <div class="form-actions">
                <button id="saveTimerBtn" class="save-btn">Save</button>
                <button id="cancelTimerBtn" class="cancel-btn">Cancel</button>
            </div>
        </div>

        <div id="timerList" class="timer-list">
            <!-- Timer items will be populated here -->
        </div>
    </div>

    <div id="mainContent" class="main-content">
        <div class="header">
            <textarea id="courseTitle" placeholder="Course Title (press Enter for new lines)"></textarea>
            <input type="number" id="courseDuration" placeholder="Duration (minutes)">
            <input type="time" id="startTime" placeholder="Start Time">
            <input type="time" id="endTime" placeholder="End Time">
            <div style="display: flex; align-items: center; margin: 0.8rem; gap: 0.5rem;">
                <input type="checkbox" id="autoStart" style="width: auto; margin: 0;">
                <label for="autoStart" style="font-size: 1.2rem; color: #2c3e50;">Auto Start</label>
            </div>
            <div class="button-group">
                <button id="startButton">Start</button>
                <button id="pauseButton">Pause</button>
                <button id="resetButton">Reset</button>
            </div>
        </div>

        <div class="container">
            <h1 class="course-title" id="displayTitle">Course Countdown Timer</h1>
            <div id="overdueMessage" class="overdue-message">OVERDUE</div>
            <div id="timer">00:00:00</div>
        </div>

        <div class="current-time-box">
            <div class="current-time-title">TIME NOW</div>
            <div id="currentTime" class="current-time">00:00</div>
        </div>
    </div>

    <script>
        let endTime;
        let timerInterval;
        let isPaused = false;
        let remainingTime = 0;
        let autoStartInterval;
        let timers = JSON.parse(localStorage.getItem('courseTimers')) || [];
        let currentTimerId = null;
        let editingTimerId = null;

        const startButton = document.getElementById('startButton');
        const pauseButton = document.getElementById('pauseButton');
        const resetButton = document.getElementById('resetButton');
        const settingsToggle = document.getElementById('settingsToggle');
        const panelToggle = document.getElementById('panelToggle');
        const sidePanel = document.getElementById('sidePanel');
        const mainContent = document.getElementById('mainContent');
        const header = document.querySelector('.header');
        const courseDuration = document.getElementById('courseDuration');
        const startTimeInput = document.getElementById('startTime');
        const endTimeInput = document.getElementById('endTime');
        const autoStartCheckbox = document.getElementById('autoStart');

        // Timer form elements
        const addTimerBtn = document.getElementById('addTimerBtn');
        const timerForm = document.getElementById('timerForm');
        const timerList = document.getElementById('timerList');
        const saveTimerBtn = document.getElementById('saveTimerBtn');
        const cancelTimerBtn = document.getElementById('cancelTimerBtn');
        const importBtn = document.getElementById('importBtn');
        const exportBtn = document.getElementById('exportBtn');
        const csvFileInput = document.getElementById('csvFileInput');

        settingsToggle.addEventListener('click', () => {
            header.style.display = header.style.display === 'none' ? 'flex' : 'none';
            settingsToggle.textContent = header.style.display === 'none' ? 'Show Settings' : 'Hide Settings';
        });

        panelToggle.addEventListener('click', () => {
            sidePanel.classList.toggle('open');
            mainContent.classList.toggle('shifted');
        });

        // Timer management functions
        function generateId() {
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        }

        function saveTimers() {
            localStorage.setItem('courseTimers', JSON.stringify(timers));
        }

        function updatePageTitle() {
            const currentTimer = timers.find(t => t.id === currentTimerId);
            if (currentTimer) {
                document.title = `${currentTimer.title} - Course Timer`;
            } else {
                document.title = 'Course Countdown Timer';
            }
        }

        function renderTimerList() {
            timerList.innerHTML = '';
            timers.forEach(timer => {
                const timerItem = document.createElement('div');
                timerItem.className = `timer-item ${timer.id === currentTimerId ? 'active' : ''}`;
                timerItem.innerHTML = `
                    <div id="timer-display-${timer.id}">
                        <h4>${timer.title}</h4>
                        <p>Duration: ${timer.duration} minutes</p>
                        ${timer.startTime ? `<p>Start: ${timer.startTime}</p>` : ''}
                        ${timer.endTime ? `<p>End: ${timer.endTime}</p>` : ''}
                        ${timer.autoStart ? '<p>Auto Start: Yes</p>' : ''}
                        <div class="timer-actions">
                            <button class="start-btn" onclick="startTimerFromPanel('${timer.id}')">Start</button>
                            <button class="edit-btn" onclick="editTimer('${timer.id}')">Edit</button>
                            <button class="delete-btn" onclick="deleteTimer('${timer.id}')">Delete</button>
                        </div>
                    </div>
                    <div id="timer-edit-${timer.id}" class="timer-edit-form" style="display: none;">
                        <label for="edit-title-${timer.id}">Title:</label>
                        <input type="text" id="edit-title-${timer.id}" value="${timer.title}" placeholder="Timer Title">

                        <label for="edit-duration-${timer.id}">Duration (minutes):</label>
                        <input type="number" id="edit-duration-${timer.id}" value="${timer.duration}" placeholder="Duration (minutes)">

                        <label for="edit-startTime-${timer.id}">Start Time:</label>
                        <input type="time" id="edit-startTime-${timer.id}" value="${timer.startTime || ''}" placeholder="Start Time">

                        <label for="edit-endTime-${timer.id}">End Time:</label>
                        <input type="time" id="edit-endTime-${timer.id}" value="${timer.endTime || ''}" placeholder="End Time">

                        <div style="display: flex; align-items: center; gap: 0.5rem; margin: 0.5rem 0;">
                            <input type="checkbox" id="edit-autoStart-${timer.id}" ${timer.autoStart ? 'checked' : ''} style="width: auto; margin: 0;">
                            <label for="edit-autoStart-${timer.id}">Auto Start</label>
                        </div>
                        <div class="form-actions">
                            <button class="save-btn" onclick="saveInlineEdit('${timer.id}')">Save</button>
                            <button class="cancel-btn" onclick="cancelInlineEdit('${timer.id}')">Cancel</button>
                        </div>
                    </div>
                `;
                timerItem.addEventListener('click', (e) => {
                    if (!e.target.classList.contains('start-btn') &&
                        !e.target.classList.contains('edit-btn') &&
                        !e.target.classList.contains('delete-btn') &&
                        !e.target.classList.contains('save-btn') &&
                        !e.target.classList.contains('cancel-btn') &&
                        !e.target.tagName === 'INPUT') {
                        selectTimer(timer.id);
                    }
                });
                timerList.appendChild(timerItem);
            });
        }

        function selectTimer(timerId) {
            const timer = timers.find(t => t.id === timerId);
            if (!timer) return;

            currentTimerId = timerId;

            // Populate main form with timer data
            document.getElementById('courseTitle').value = timer.title;
            document.getElementById('courseDuration').value = timer.duration;
            document.getElementById('startTime').value = timer.startTime || '';
            document.getElementById('endTime').value = timer.endTime || '';
            document.getElementById('autoStart').checked = timer.autoStart || false;

            updatePageTitle();
            renderTimerList();
        }

        function startTimerFromPanel(timerId) {
            selectTimer(timerId);
            startTimer();
        }

        function showTimerForm(timer = null) {
            editingTimerId = timer ? timer.id : null;

            if (timer) {
                document.getElementById('formTitle').value = timer.title;
                document.getElementById('formDuration').value = timer.duration;
                document.getElementById('formStartTime').value = timer.startTime || '';
                document.getElementById('formEndTime').value = timer.endTime || '';
                document.getElementById('formAutoStart').checked = timer.autoStart || false;
            } else {
                document.getElementById('formTitle').value = '';
                document.getElementById('formDuration').value = '';
                document.getElementById('formStartTime').value = '';
                document.getElementById('formEndTime').value = '';
                document.getElementById('formAutoStart').checked = false;
            }

            timerForm.classList.add('show');
        }

        function hideTimerForm() {
            timerForm.classList.remove('show');
            editingTimerId = null;
        }

        function saveTimer() {
            const title = document.getElementById('formTitle').value.trim();
            const duration = parseInt(document.getElementById('formDuration').value);
            const startTime = document.getElementById('formStartTime').value;
            const endTime = document.getElementById('formEndTime').value;
            const autoStart = document.getElementById('formAutoStart').checked;

            if (!title || !duration || duration <= 0) {
                alert('Please enter a valid title and duration');
                return;
            }

            const timerData = {
                title,
                duration,
                startTime,
                endTime,
                autoStart
            };

            if (editingTimerId) {
                // Edit existing timer
                const index = timers.findIndex(t => t.id === editingTimerId);
                if (index !== -1) {
                    timers[index] = { ...timers[index], ...timerData };
                }
            } else {
                // Add new timer
                timerData.id = generateId();
                timers.push(timerData);
            }

            saveTimers();
            renderTimerList();
            hideTimerForm();
        }

        function editTimer(timerId) {
            // Hide all other edit forms
            timers.forEach(timer => {
                if (timer.id !== timerId) {
                    cancelInlineEdit(timer.id);
                }
            });

            // Show the edit form for this timer
            document.getElementById(`timer-display-${timerId}`).style.display = 'none';
            document.getElementById(`timer-edit-${timerId}`).style.display = 'block';

            // Set up auto-calculation for this edit form
            setupInlineAutoCalculation(timerId);
        }

        function cancelInlineEdit(timerId) {
            document.getElementById(`timer-display-${timerId}`).style.display = 'block';
            document.getElementById(`timer-edit-${timerId}`).style.display = 'none';
        }

        function saveInlineEdit(timerId) {
            const title = document.getElementById(`edit-title-${timerId}`).value.trim();
            const duration = parseInt(document.getElementById(`edit-duration-${timerId}`).value);
            const startTime = document.getElementById(`edit-startTime-${timerId}`).value;
            const endTime = document.getElementById(`edit-endTime-${timerId}`).value;
            const autoStart = document.getElementById(`edit-autoStart-${timerId}`).checked;

            if (!title || !duration || duration <= 0) {
                alert('Please enter a valid title and duration');
                return;
            }

            const timerIndex = timers.findIndex(t => t.id === timerId);
            if (timerIndex !== -1) {
                timers[timerIndex] = {
                    ...timers[timerIndex],
                    title,
                    duration,
                    startTime,
                    endTime,
                    autoStart
                };

                saveTimers();
                renderTimerList();

                // Update main screen if this timer is currently selected
                if (currentTimerId === timerId) {
                    selectTimer(timerId);
                }
            }
        }

        function setupInlineAutoCalculation(timerId) {
            const durationInput = document.getElementById(`edit-duration-${timerId}`);
            const startTimeInput = document.getElementById(`edit-startTime-${timerId}`);
            const endTimeInput = document.getElementById(`edit-endTime-${timerId}`);

            function calculateInlineEndTime() {
                const startTime = startTimeInput.value;
                const duration = parseInt(durationInput.value);

                if (startTime && duration && duration > 0) {
                    const [hours, minutes] = startTime.split(':').map(Number);
                    const startDate = new Date();
                    startDate.setHours(hours, minutes, 0, 0);

                    const endDate = new Date(startDate.getTime() + duration * 60 * 1000);
                    const endHours = String(endDate.getHours()).padStart(2, '0');
                    const endMinutes = String(endDate.getMinutes()).padStart(2, '0');

                    endTimeInput.value = `${endHours}:${endMinutes}`;
                }
            }

            function calculateInlineDuration() {
                const startTime = startTimeInput.value;
                const endTime = endTimeInput.value;

                if (startTime && endTime) {
                    const [startHours, startMinutes] = startTime.split(':').map(Number);
                    const [endHours, endMinutes] = endTime.split(':').map(Number);

                    const startDate = new Date();
                    startDate.setHours(startHours, startMinutes, 0, 0);

                    const endDate = new Date();
                    endDate.setHours(endHours, endMinutes, 0, 0);

                    if (endDate < startDate) {
                        endDate.setDate(endDate.getDate() + 1);
                    }

                    const durationMs = endDate.getTime() - startDate.getTime();
                    const durationMinutes = Math.round(durationMs / (60 * 1000));

                    if (durationMinutes > 0) {
                        durationInput.value = durationMinutes;
                    }
                }
            }

            // Remove existing event listeners to avoid duplicates
            durationInput.removeEventListener('input', calculateInlineEndTime);
            startTimeInput.removeEventListener('input', calculateInlineEndTime);
            startTimeInput.removeEventListener('input', calculateInlineDuration);
            endTimeInput.removeEventListener('input', calculateInlineDuration);

            // Add event listeners
            durationInput.addEventListener('input', calculateInlineEndTime);
            startTimeInput.addEventListener('input', () => {
                if (durationInput.value && startTimeInput.value) {
                    calculateInlineEndTime();
                } else if (endTimeInput.value && startTimeInput.value) {
                    calculateInlineDuration();
                }
            });
            endTimeInput.addEventListener('input', calculateInlineDuration);
        }

        function deleteTimer(timerId) {
            if (confirm('Are you sure you want to delete this timer?')) {
                timers = timers.filter(t => t.id !== timerId);
                if (currentTimerId === timerId) {
                    currentTimerId = null;
                    resetTimer();
                    updatePageTitle();
                }
                saveTimers();
                renderTimerList();
            }
        }

        // Import/Export functions
        function exportTimersToCSV() {
            if (timers.length === 0) {
                alert('No timers to export');
                return;
            }

            const headers = ['Title', 'Duration (minutes)', 'Start Time', 'End Time', 'Auto Start'];
            const csvContent = [
                headers.join(','),
                ...timers.map(timer => [
                    `"${timer.title.replace(/"/g, '""')}"`, // Escape quotes in title
                    timer.duration,
                    timer.startTime || '',
                    timer.endTime || '',
                    timer.autoStart ? 'Yes' : 'No'
                ].join(','))
            ].join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'course_timers.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function importTimersFromCSV(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const csv = e.target.result;
                    const lines = csv.split('\n').filter(line => line.trim());

                    if (lines.length < 2) {
                        alert('CSV file appears to be empty or invalid');
                        return;
                    }

                    // Skip header row
                    const dataLines = lines.slice(1);
                    const importedTimers = [];

                    dataLines.forEach((line, index) => {
                        try {
                            // Simple CSV parsing (handles quoted fields)
                            const fields = [];
                            let current = '';
                            let inQuotes = false;

                            for (let i = 0; i < line.length; i++) {
                                const char = line[i];
                                if (char === '"') {
                                    if (inQuotes && line[i + 1] === '"') {
                                        current += '"';
                                        i++; // Skip next quote
                                    } else {
                                        inQuotes = !inQuotes;
                                    }
                                } else if (char === ',' && !inQuotes) {
                                    fields.push(current);
                                    current = '';
                                } else {
                                    current += char;
                                }
                            }
                            fields.push(current); // Add last field

                            if (fields.length >= 2) {
                                const title = fields[0].trim();
                                const duration = parseInt(fields[1]);
                                const startTime = fields[2] ? fields[2].trim() : '';
                                const endTime = fields[3] ? fields[3].trim() : '';
                                const autoStart = fields[4] ? fields[4].trim().toLowerCase() === 'yes' : false;

                                if (title && duration && duration > 0) {
                                    importedTimers.push({
                                        id: generateId(),
                                        title,
                                        duration,
                                        startTime,
                                        endTime,
                                        autoStart
                                    });
                                }
                            }
                        } catch (error) {
                            console.warn(`Error parsing line ${index + 2}:`, error);
                        }
                    });

                    if (importedTimers.length === 0) {
                        alert('No valid timers found in CSV file');
                        return;
                    }

                    const confirmMessage = `Found ${importedTimers.length} timer(s) to import. This will add to your existing timers. Continue?`;
                    if (confirm(confirmMessage)) {
                        timers.push(...importedTimers);
                        saveTimers();
                        renderTimerList();
                        alert(`Successfully imported ${importedTimers.length} timer(s)`);
                    }

                } catch (error) {
                    alert('Error reading CSV file: ' + error.message);
                }
            };
            reader.readAsText(file);
        }

        // Event listeners for import/export
        exportBtn.addEventListener('click', exportTimersToCSV);
        importBtn.addEventListener('click', () => csvFileInput.click());
        csvFileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                importTimersFromCSV(e.target.files[0]);
                e.target.value = ''; // Reset file input
            }
        });

        // Event listeners for timer form
        addTimerBtn.addEventListener('click', () => showTimerForm());
        saveTimerBtn.addEventListener('click', saveTimer);
        cancelTimerBtn.addEventListener('click', hideTimerForm);

        // Auto-calculation functions for timer form
        function calculateFormEndTime() {
            const startTime = document.getElementById('formStartTime').value;
            const duration = parseInt(document.getElementById('formDuration').value);

            if (startTime && duration && duration > 0) {
                const [hours, minutes] = startTime.split(':').map(Number);
                const startDate = new Date();
                startDate.setHours(hours, minutes, 0, 0);

                const endDate = new Date(startDate.getTime() + duration * 60 * 1000);
                const endHours = String(endDate.getHours()).padStart(2, '0');
                const endMinutes = String(endDate.getMinutes()).padStart(2, '0');

                document.getElementById('formEndTime').value = `${endHours}:${endMinutes}`;
            }
        }

        function calculateFormDuration() {
            const startTime = document.getElementById('formStartTime').value;
            const endTime = document.getElementById('formEndTime').value;

            if (startTime && endTime) {
                const [startHours, startMinutes] = startTime.split(':').map(Number);
                const [endHours, endMinutes] = endTime.split(':').map(Number);

                const startDate = new Date();
                startDate.setHours(startHours, startMinutes, 0, 0);

                const endDate = new Date();
                endDate.setHours(endHours, endMinutes, 0, 0);

                if (endDate < startDate) {
                    endDate.setDate(endDate.getDate() + 1);
                }

                const durationMs = endDate.getTime() - startDate.getTime();
                const durationMinutes = Math.round(durationMs / (60 * 1000));

                if (durationMinutes > 0) {
                    document.getElementById('formDuration').value = durationMinutes;
                }
            }
        }

        // Event listeners for form auto-calculation
        document.getElementById('formDuration').addEventListener('input', () => {
            if (document.getElementById('formStartTime').value && document.getElementById('formDuration').value) {
                calculateFormEndTime();
            }
        });

        document.getElementById('formStartTime').addEventListener('input', () => {
            if (document.getElementById('formDuration').value && document.getElementById('formStartTime').value) {
                calculateFormEndTime();
            } else if (document.getElementById('formEndTime').value && document.getElementById('formStartTime').value) {
                calculateFormDuration();
            }
        });

        document.getElementById('formEndTime').addEventListener('input', () => {
            if (document.getElementById('formStartTime').value && document.getElementById('formEndTime').value) {
                calculateFormDuration();
            }
        });

        // Auto-calculation functions
        function calculateEndTime() {
            const startTime = startTimeInput.value;
            const duration = parseInt(courseDuration.value);

            if (startTime && duration && duration > 0) {
                const [hours, minutes] = startTime.split(':').map(Number);
                const startDate = new Date();
                startDate.setHours(hours, minutes, 0, 0);

                const endDate = new Date(startDate.getTime() + duration * 60 * 1000);
                const endHours = String(endDate.getHours()).padStart(2, '0');
                const endMinutes = String(endDate.getMinutes()).padStart(2, '0');

                endTimeInput.value = `${endHours}:${endMinutes}`;
            }
        }

        function calculateDuration() {
            const startTime = startTimeInput.value;
            const endTime = endTimeInput.value;

            if (startTime && endTime) {
                const [startHours, startMinutes] = startTime.split(':').map(Number);
                const [endHours, endMinutes] = endTime.split(':').map(Number);

                const startDate = new Date();
                startDate.setHours(startHours, startMinutes, 0, 0);

                const endDate = new Date();
                endDate.setHours(endHours, endMinutes, 0, 0);

                // Handle case where end time is next day
                if (endDate < startDate) {
                    endDate.setDate(endDate.getDate() + 1);
                }

                const durationMs = endDate.getTime() - startDate.getTime();
                const durationMinutes = Math.round(durationMs / (60 * 1000));

                if (durationMinutes > 0) {
                    courseDuration.value = durationMinutes;
                }
            }
        }

        // Event listeners for auto-calculation
        courseDuration.addEventListener('input', () => {
            if (startTimeInput.value && courseDuration.value) {
                calculateEndTime();
            }
        });

        startTimeInput.addEventListener('input', () => {
            if (courseDuration.value && startTimeInput.value) {
                calculateEndTime();
            } else if (endTimeInput.value && startTimeInput.value) {
                calculateDuration();
            }
        });

        endTimeInput.addEventListener('input', () => {
            if (startTimeInput.value && endTimeInput.value) {
                calculateDuration();
            }
        });

        function updateCurrentTime() {
            const now = new Date();
            document.getElementById('currentTime').textContent =
                `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;

            // Check for auto-start
            checkAutoStart(now);
        }

        function checkAutoStart(now) {
            if (autoStartCheckbox.checked && startTimeInput.value && !endTime && !isPaused) {
                const [startHours, startMinutes] = startTimeInput.value.split(':').map(Number);
                const currentHours = now.getHours();
                const currentMinutes = now.getMinutes();

                if (currentHours === startHours && currentMinutes === startMinutes) {
                    startTimer();
                }
            }
        }

        setInterval(updateCurrentTime, 1000);
        updateCurrentTime();

        function calculateMaxLineWidth(lines, fontSize) {
            const measurer = document.createElement('div');
            measurer.style.position = 'absolute';
            measurer.style.visibility = 'hidden';
            measurer.style.whiteSpace = 'nowrap';
            measurer.style.font = `${fontSize}px 'Segoe UI'`;
            document.body.appendChild(measurer);

            let maxWidth = 0;
            lines.forEach(line => {
                measurer.textContent = line;
                maxWidth = Math.max(maxWidth, measurer.offsetWidth);
            });
            document.body.removeChild(measurer);
            return maxWidth;
        }

        function autoSizeText() {
            const titleElement = document.getElementById('displayTitle');
            const container = document.querySelector('.container');
            const lines = titleElement.textContent.split('\n');
            const containerWidth = container.offsetWidth - 40;
            
            let low = 20;
            let high = 56;
            let bestSize = high;

            for (let i = 0; i < 10; i++) {
                const mid = Math.floor((low + high) / 2);
                const currentWidth = calculateMaxLineWidth(lines, mid);
                currentWidth <= containerWidth ? (bestSize = mid, low = mid + 1) : (high = mid - 1);
            }

            titleElement.style.fontSize = `${bestSize}px`;
        }

        function startTimer() {
            if (isPaused) {
                endTime = Date.now() + remainingTime;
                isPaused = false;
            } else {
                const title = document.getElementById('courseTitle').value || 'Course Countdown Timer';
                const duration = parseInt(document.getElementById('courseDuration').value) || 0;
                
                if (duration <= 0) return alert('Please enter valid duration');
                
                const titleElement = document.getElementById('displayTitle');
                titleElement.innerHTML = title.split('\n').map(line => 
                    `<div class="title-line">${line}</div>`
                ).join('');
                
                autoSizeText();
                remainingTime = duration * 60 * 1000;
                endTime = Date.now() + remainingTime;
            }
            
            clearInterval(timerInterval);
            timerInterval = setInterval(updateTimer, 1000);
            updateTimer();
        }

        function togglePause() {
            if (!isPaused && endTime) {
                remainingTime = endTime - Date.now();
                clearInterval(timerInterval);
                isPaused = true;
                pauseButton.textContent = 'Resume';
            } else {
                isPaused = false;
                pauseButton.textContent = 'Pause';
                startTimer();
            }
        }

        function resetTimer() {
            clearInterval(timerInterval);
            isPaused = false;
            remainingTime = 0;
            endTime = null;
            document.getElementById('timer').textContent = '00:00:00';
            document.getElementById('overdueMessage').style.display = 'none';
            document.getElementById('displayTitle').style.fontSize = '56px';
            document.getElementById('displayTitle').textContent = 'Course Countdown Timer';
            pauseButton.textContent = 'Pause';

            // Only clear fields if no timer is selected
            if (!currentTimerId) {
                document.getElementById('courseTitle').value = '';
                document.getElementById('courseDuration').value = '';
                startTimeInput.value = '';
                endTimeInput.value = '';
                autoStartCheckbox.checked = false;
                updatePageTitle();
            }

            header.style.display = 'flex';
            settingsToggle.textContent = 'Hide Settings';
        }

        function updateTimer() {
            const now = Date.now();
            const remaining = endTime - now;
            const overdueMessage = document.getElementById('overdueMessage');

            if (remaining < 0) {
                const overdue = Math.abs(remaining);
                document.getElementById('timer').innerHTML = 
                    `<span class="overdue">+${Math.floor(overdue/(36e5)).toString().padStart(2,'0')}:` +
                    `${Math.floor((overdue%36e5)/6e4).toString().padStart(2,'0')}:` +
                    `${Math.floor((overdue%6e4)/1e3).toString().padStart(2,'0')}</span>`;
                overdueMessage.style.display = 'block';
                return;
            }

            overdueMessage.style.display = 'none';
            document.getElementById('timer').innerHTML = 
                `${Math.floor(remaining/36e5).toString().padStart(2,'0')}:` +
                `${Math.floor((remaining%36e5)/6e4).toString().padStart(2,'0')}:` +
                `${Math.floor((remaining%6e4)/1e3).toString().padStart(2,'0')}`;
        }

        startButton.addEventListener('click', startTimer);
        pauseButton.addEventListener('click', togglePause);
        resetButton.addEventListener('click', resetTimer);
        window.addEventListener('resize', () => {
            if (document.getElementById('displayTitle').textContent !== 'Course Countdown Timer') {
                autoSizeText();
            }
        });

        // Make functions global for onclick handlers
        window.editTimer = editTimer;
        window.deleteTimer = deleteTimer;
        window.startTimerFromPanel = startTimerFromPanel;
        window.saveInlineEdit = saveInlineEdit;
        window.cancelInlineEdit = cancelInlineEdit;

        // Initialize the app
        renderTimerList();
        updatePageTitle();
    </script>
</body>
</html>